using UnityEngine;
using Kamgam.SandGame;
using Unity.Mathematics;

namespace Kamgam.SandGame
{
    /// <summary>
    /// Advanced demo script showcasing Slime material features
    /// </summary>
    public class SlimeDemo : MonoBehaviour
    {
        [Header("Slime Demo Settings")]
        [SerializeField] private KeyCode spawnSlimeKey = KeyCode.S;
        [SerializeField] private KeyCode spawnSandKey = KeyCode.D;
        [SerializeField] private KeyCode spawnWaterKey = KeyCode.W;
        [SerializeField] private KeyCode clearAllKey = KeyCode.C;
        
        [Header("Spawn Settings")]
        [SerializeField] private int spawnRadius = 15;
        [SerializeField] private bool continuousSpawn = false;
        
        [Header("Slime Colors")]
        [SerializeField] private Color[] slimeColors = {
            new Color(0.2f, 0.8f, 0.3f, 1f), // Green
            new Color(0.8f, 0.2f, 0.8f, 1f), // Purple  
            new Color(0.2f, 0.5f, 0.8f, 1f), // Blue
            new Color(0.8f, 0.6f, 0.2f, 1f)  // Orange
        };
        
        private PixelWorld pixelWorld;
        private Camera mainCamera;
        private int currentColorIndex = 0;
        
        void Start()
        {
            pixelWorld = FindObjectOfType<SandWorld>().PixelWorld;
            mainCamera = Camera.main;
            
            if (pixelWorld == null)
            {
                Debug.LogError("SlimeDemo: No PixelWorld found in scene!");
                enabled = false;
                return;
            }
            
            if (mainCamera == null)
            {
                Debug.LogError("SlimeDemo: No main camera found!");
                enabled = false;
                return;
            }
            
            Debug.Log("Slime Demo loaded! Use S for slime, D for sand, W for water, C to clear");
        }
        
        void Update()
        {
            HandleInput();
            
            if (continuousSpawn && Input.GetMouseButton(0))
            {
                SpawnSlimeAtMousePosition();
            }
        }
        
        private void HandleInput()
        {
            if (Input.GetKeyDown(spawnSlimeKey) || (continuousSpawn && Input.GetMouseButtonDown(0)))
            {
                SpawnSlimeAtMousePosition();
            }
            
            if (Input.GetKeyDown(spawnSandKey))
            {
                SpawnMaterialAtMousePosition(PixelMaterialId.Sand);
            }
            
            if (Input.GetKeyDown(spawnWaterKey))
            {
                SpawnMaterialAtMousePosition(PixelMaterialId.Water);
            }
            
            if (Input.GetKeyDown(clearAllKey))
            {
                ClearAllPixels();
            }
            
            // Cycle through slime colors with number keys
            for (int i = 1; i <= slimeColors.Length && i <= 9; i++)
            {
                if (Input.GetKeyDown(KeyCode.Alpha0 + i))
                {
                    currentColorIndex = i - 1;
                    Debug.Log($"Switched to slime color {i}: {slimeColors[currentColorIndex]}");
                }
            }
        }
        
        private void SpawnSlimeAtMousePosition()
        {
            Vector3 mouseWorldPos = mainCamera.ScreenToWorldPoint(Input.mousePosition);
            mouseWorldPos.z = 0;
            Vector3 pixelPos = pixelWorld.WorldToPixelPos(mouseWorldPos);
            
            SpawnSlimeCircle((int)pixelPos.x, (int)pixelPos.y, spawnRadius, slimeColors[currentColorIndex]);
        }
        
        private void SpawnMaterialAtMousePosition(PixelMaterialId materialId)
        {
            Vector3 mouseWorldPos = mainCamera.ScreenToWorldPoint(Input.mousePosition);
            mouseWorldPos.z = 0;
            Vector3 pixelPos = pixelWorld.WorldToPixelPos(mouseWorldPos);
            
            SpawnMaterialCircle((int)pixelPos.x, (int)pixelPos.y, spawnRadius, materialId);
        }
        
        private void SpawnSlimeCircle(int centerX, int centerY, int radius, Color color)
        {
            var rnd = Unity.Mathematics.Random.CreateFromIndex((uint)UnityEngine.Random.Range(0, 10000));
            
            for (int x = centerX - radius; x <= centerX + radius; x++)
            {
                for (int y = centerY - radius; y <= centerY + radius; y++)
                {
                    float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    if (distance <= radius)
                    {
                        // Add some randomness to make it look more organic
                        if (rnd.NextFloat() > 0.1f) // 90% fill rate for organic look
                        {
                            var pixel = PixelFactory.CreatePixel(
                                x, y,
                                isEmpty: false,
                                isLoaded: true,
                                ref LevelInfo.GetLoadedLevelInfo(pixelWorld.Level).Materials.NativeMaterials,
                                PixelMaterialId.Slime,
                                ref rnd
                            );
                            
                            // Set custom color
                            pixel.r = (byte)(color.r * 255);
                            pixel.g = (byte)(color.g * 255);
                            pixel.b = (byte)(color.b * 255);
                            pixel.a = (byte)(color.a * 255);
                            
                            pixelWorld.SchedulePixelForDrawing(pixel);
                        }
                    }
                }
            }
        }
        
        private void SpawnMaterialCircle(int centerX, int centerY, int radius, PixelMaterialId materialId)
        {
            var rnd = Unity.Mathematics.Random.CreateFromIndex((uint)UnityEngine.Random.Range(0, 10000));
            
            for (int x = centerX - radius; x <= centerX + radius; x++)
            {
                for (int y = centerY - radius; y <= centerY + radius; y++)
                {
                    float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    if (distance <= radius)
                    {
                        var pixel = PixelFactory.CreatePixel(
                            x, y,
                            isEmpty: false,
                            isLoaded: true,
                            ref LevelInfo.GetLoadedLevelInfo(pixelWorld.Level).Materials.NativeMaterials,
                            materialId,
                            ref rnd
                        );
                        
                        pixelWorld.SchedulePixelForDrawing(pixel);
                    }
                }
            }
        }
        
        private void ClearAllPixels()
        {
            // This is a simple implementation - in a real game you might want a more efficient method
            Debug.Log("Clearing all pixels... (This might take a moment)");

            // You would need to implement a clear method in PixelWorld or use the existing level loading system
            // For now, just log the action
            Debug.Log("Clear functionality would need to be implemented in PixelWorld");
        }

        void OnGUI()
        {
            int yOffset = 10;
            int lineHeight = 20;

            GUI.Label(new Rect(10, yOffset, 400, lineHeight), $"Slime Demo Controls:");
            yOffset += lineHeight;

            GUI.Label(new Rect(10, yOffset, 400, lineHeight), $"'{spawnSlimeKey}' - Spawn Slime | '{spawnSandKey}' - Spawn Sand | '{spawnWaterKey}' - Spawn Water");
            yOffset += lineHeight;

            GUI.Label(new Rect(10, yOffset, 400, lineHeight), $"'{clearAllKey}' - Clear All | 1-{slimeColors.Length} - Change Slime Color");
            yOffset += lineHeight;

            GUI.Label(new Rect(10, yOffset, 400, lineHeight), $"Current Slime Color: {currentColorIndex + 1} | Radius: {spawnRadius}");
            yOffset += lineHeight;

            GUI.Label(new Rect(10, yOffset, 400, lineHeight), $"Continuous Spawn: {continuousSpawn} (Hold mouse to spawn)");
            yOffset += lineHeight + 10;

            // Color preview
            Color currentColor = slimeColors[currentColorIndex];
            GUI.color = currentColor;
            GUI.Box(new Rect(10, yOffset, 30, 20), "");
            GUI.color = Color.white;
            GUI.Label(new Rect(50, yOffset, 200, 20), $"RGB({currentColor.r:F2}, {currentColor.g:F2}, {currentColor.b:F2})");

            // Instructions
            yOffset += 40;
            GUI.Label(new Rect(10, yOffset, 500, lineHeight), "Slime Features:");
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 500, lineHeight), "• Flows like liquid but with more viscosity");
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 500, lineHeight), "• Automatic edge darkening for cohesive blob appearance");
            yOffset += lineHeight;
            GUI.Label(new Rect(10, yOffset, 500, lineHeight), "• Different colors can be mixed together");
        }
    }
}
