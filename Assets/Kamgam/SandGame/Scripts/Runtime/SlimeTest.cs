using UnityEngine;
using Kamgam.SandGame;

namespace Kamgam.SandGame
{
    /// <summary>
    /// Test script for Slime material functionality
    /// </summary>
    public class SlimeTest : MonoBehaviour
    {
        [Header("Slime Test Settings")]
        [SerializeField] private KeyCode spawnKey = KeyCode.S;
        [SerializeField] private int spawnRadius = 10;
        [SerializeField] private Color slimeColor = new Color(0.2f, 0.8f, 0.3f, 1f); // Green slime
        
        private PixelWorld pixelWorld;
        private Camera mainCamera;
        
        void Start()
        {
            // Find the pixel world in the scene
            pixelWorld = FindObjectOfType<SandWorld>().PixelWorld;
            mainCamera = Camera.main;
            
            if (pixelWorld == null)
            {
                Debug.LogError("SlimeTest: No PixelWorld found in scene!");
                enabled = false;
                return;
            }
            
            if (mainCamera == null)
            {
                Debug.LogError("SlimeTest: No main camera found!");
                enabled = false;
                return;
            }
        }
        
        void Update()
        {
            if (Input.GetKeyDown(spawnKey))
            {
                SpawnSlimeAtMousePosition();
            }
        }
        
        private void SpawnSlimeAtMousePosition()
        {
            // Get mouse position in world coordinates
            Vector3 mouseWorldPos = mainCamera.ScreenToWorldPoint(Input.mousePosition);
            mouseWorldPos.z = 0;
            
            // Convert to pixel coordinates
            Vector3 pixelPos = pixelWorld.WorldToPixelPos(mouseWorldPos);
            
            // Spawn slime in a circular pattern
            SpawnSlimeCircle((int)pixelPos.x, (int)pixelPos.y, spawnRadius);
            
            Debug.Log($"Spawned slime at pixel position: {pixelPos.x}, {pixelPos.y}");
        }
        
        private void SpawnSlimeCircle(int centerX, int centerY, int radius)
        {
            for (int x = centerX - radius; x <= centerX + radius; x++)
            {
                for (int y = centerY - radius; y <= centerY + radius; y++)
                {
                    // Check if point is within circle
                    float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    if (distance <= radius)
                    {
                        // Create slime pixel
                        var pixel = PixelFactory.CreatePixel(
                            x, y,
                            isEmpty: false,
                            isLoaded: true,
                            ref LevelInfo.GetLoadedLevelInfo(pixelWorld.Level).Materials.NativeMaterials,
                            PixelMaterialId.Slime,
                            ref Unity.Mathematics.Random.CreateFromIndex((uint)(x * 1000 + y))
                        );
                        
                        // Set custom slime color
                        pixel.r = (byte)(slimeColor.r * 255);
                        pixel.g = (byte)(slimeColor.g * 255);
                        pixel.b = (byte)(slimeColor.b * 255);
                        pixel.a = (byte)(slimeColor.a * 255);
                        
                        // Add pixel to world
                        pixelWorld.SchedulePixelForDrawing(pixel);
                    }
                }
            }
        }
        
        void OnGUI()
        {
            GUI.Label(new Rect(10, 10, 300, 20), $"Press '{spawnKey}' to spawn slime at mouse position");
            GUI.Label(new Rect(10, 30, 300, 20), $"Spawn radius: {spawnRadius}");
        }
    }
}
