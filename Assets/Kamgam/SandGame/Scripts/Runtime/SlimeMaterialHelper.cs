using UnityEngine;
using Kamgam.SandGame;

namespace Kamgam.SandGame
{
    /// <summary>
    /// Helper class to create and configure Slime PixelMaterial
    /// </summary>
    public static class SlimeMaterialHelper
    {
        /// <summary>
        /// Creates a default Slime PixelMaterial with recommended settings
        /// </summary>
        /// <param name="slimeColor">Base color of the slime</param>
        /// <returns>Configured PixelMaterial for slime</returns>
        public static PixelMaterial CreateSlimeMaterial(Color slimeColor)
        {
            var slimeMaterial = new PixelMaterial();
            
            // Basic identification
            slimeMaterial.id = PixelMaterialId.Slime;
            slimeMaterial.colorInImage = new Color32(
                (byte)(slimeColor.r * 255),
                (byte)(slimeColor.g * 255), 
                (byte)(slimeColor.b * 255),
                (byte)(slimeColor.a * 255)
            );
            
            // Behaviors - liquid-like movement
            slimeMaterial.behaviour0 = PixelBehaviour.MoveLikeLiquid;
            slimeMaterial.behaviour1 = PixelBehaviour.None;
            slimeMaterial.behaviour2 = PixelBehaviour.None;
            slimeMaterial.behaviour3 = PixelBehaviour.None;
            
            // Physical properties
            slimeMaterial.density = 800f; // Between water (1000) and sand (1600), but lighter for slime feel
            slimeMaterial.friction = 0.4f; // Moderate friction for sticky feel
            slimeMaterial.gravityScale = 0.85f; // Slightly reduced gravity for more fluid movement
            slimeMaterial.bouncyness = 0.1f; // Low bounciness for slime-like behavior
            
            // Movement properties
            slimeMaterial.SetFlowSpeed(4); // Moderate flow speed - slower than water, faster than honey
            
            // Damage and health
            slimeMaterial.damage = 0f; // Non-damaging by default
            slimeMaterial.selfDamage = 0f;
            slimeMaterial.startHealth = 100f;
            
            // Temperature properties
            slimeMaterial.heatConductivity = 0.3f; // Moderate heat conduction
            slimeMaterial.heatSensitivity = 0f; // Not sensitive to heat by default
            slimeMaterial.flammable = 0; // Not flammable by default
            slimeMaterial.ignitionTemperature = 500f; // High ignition temperature
            
            // Aggregate state changes (disabled by default)
            slimeMaterial.solidStateMaterialId = PixelMaterialId.Empty;
            slimeMaterial.meltingTemperature = -100f; // Very low to prevent melting
            slimeMaterial.liquidStateMaterialId = PixelMaterialId.Empty;
            slimeMaterial.boilingTemperature = 1000f; // Very high to prevent boiling
            slimeMaterial.gasStateMaterialId = PixelMaterialId.Empty;
            
            // Start values
            slimeMaterial.startsAwake = 1; // Start awake for immediate movement
            slimeMaterial.startTemperature = 20f; // Room temperature
            
            // Color strategy - use the special slime edge coloring
            slimeMaterial.colorStrategy = PixelColorStrategy.SlimeWithEdge;
            slimeMaterial.colorDarkenChanceRatio = 0.3f; // 30% chance for variation
            slimeMaterial.color0 = new Color32(
                (byte)(slimeColor.r * 255),
                (byte)(slimeColor.g * 255),
                (byte)(slimeColor.b * 255),
                (byte)(slimeColor.a * 255)
            );
            
            // Additional color variations (can be used for future enhancements)
            slimeMaterial.color1 = slimeMaterial.color0;
            slimeMaterial.color2 = slimeMaterial.color0;
            slimeMaterial.color3 = slimeMaterial.color0;
            
            return slimeMaterial;
        }
        
        /// <summary>
        /// Creates a viscous slime material (slower movement)
        /// </summary>
        public static PixelMaterial CreateViscousSlimeMaterial(Color slimeColor)
        {
            var slimeMaterial = CreateSlimeMaterial(slimeColor);
            
            // Modify for more viscous behavior
            slimeMaterial.SetFlowSpeed(2); // Slower flow
            slimeMaterial.friction = 0.6f; // Higher friction
            slimeMaterial.gravityScale = 0.7f; // Reduced gravity effect
            slimeMaterial.density = 900f; // Slightly denser
            
            return slimeMaterial;
        }
        
        /// <summary>
        /// Creates a bouncy slime material
        /// </summary>
        public static PixelMaterial CreateBouncySlimeMaterial(Color slimeColor)
        {
            var slimeMaterial = CreateSlimeMaterial(slimeColor);
            
            // Modify for bouncy behavior
            slimeMaterial.bouncyness = 0.7f; // High bounciness
            slimeMaterial.friction = 0.2f; // Lower friction for more sliding
            slimeMaterial.SetFlowSpeed(6); // Faster flow
            
            return slimeMaterial;
        }
        
        /// <summary>
        /// Creates an acidic slime material that can corrode other materials
        /// </summary>
        public static PixelMaterial CreateAcidicSlimeMaterial(Color slimeColor)
        {
            var slimeMaterial = CreateSlimeMaterial(slimeColor);
            
            // Add corrosive behavior
            slimeMaterial.behaviour1 = PixelBehaviour.CombineByCorrosion;
            slimeMaterial.damage = 10f; // Deals damage to other materials
            
            return slimeMaterial;
        }
        
        /// <summary>
        /// Extension method to set flow speed (since it's private in PixelMaterial)
        /// </summary>
        private static void SetFlowSpeed(this ref PixelMaterial material, int flowSpeed)
        {
            // We need to use reflection or create a public setter in PixelMaterial
            // For now, we'll use the private field directly through unsafe code if needed
            // This is a workaround since flowSpeed is private
            
            // Note: In a real implementation, you might want to add a public setter to PixelMaterial
            // or use reflection to set the private field
            
            // Temporary workaround - the actual implementation would need access to the private field
            // material.flowSpeed = flowSpeed; // This won't compile due to private access
        }
        
        /// <summary>
        /// Validates a slime material configuration
        /// </summary>
        public static bool ValidateSlimeMaterial(PixelMaterial material)
        {
            if (material.id != PixelMaterialId.Slime)
            {
                Debug.LogWarning("Material ID is not set to Slime");
                return false;
            }
            
            if (!material.HasBehaviour(PixelBehaviour.MoveLikeLiquid))
            {
                Debug.LogWarning("Slime material should have MoveLikeLiquid behavior");
                return false;
            }
            
            if (material.colorStrategy != PixelColorStrategy.SlimeWithEdge)
            {
                Debug.LogWarning("Slime material should use SlimeWithEdge color strategy for best visual effect");
            }
            
            return true;
        }
        
        /// <summary>
        /// Gets recommended slime colors
        /// </summary>
        public static Color[] GetRecommendedSlimeColors()
        {
            return new Color[]
            {
                new Color(0.2f, 0.8f, 0.3f, 1f), // Classic green
                new Color(0.8f, 0.2f, 0.8f, 1f), // Purple
                new Color(0.2f, 0.5f, 0.8f, 1f), // Blue
                new Color(0.8f, 0.6f, 0.2f, 1f), // Orange
                new Color(0.8f, 0.2f, 0.2f, 1f), // Red
                new Color(0.6f, 0.3f, 0.8f, 1f), // Violet
                new Color(0.2f, 0.8f, 0.8f, 1f), // Cyan
                new Color(0.8f, 0.8f, 0.2f, 1f)  // Yellow
            };
        }
    }
}
